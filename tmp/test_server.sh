#!/bin/bash

# File Server Test Script
# This script tests the basic functionality of the file server

set -e

SERVER_URL="http://localhost:8080"
TEST_FILE="/tmp/test_upload.txt"

echo "🚀 Testing File Server Functionality"
echo "======================================"

# Create test file
echo "This is a test file for upload" > "$TEST_FILE"

echo "1. Testing health check..."
HEALTH=$(curl -s "$SERVER_URL/health")
echo "✅ Health check: $HEALTH"

echo ""
echo "2. Testing file listing..."
FILES=$(curl -s "$SERVER_URL/api/files/")
echo "✅ File listing successful"
echo "   Files found: $(echo "$FILES" | jq -r '.total')"

echo ""
echo "3. Testing file upload..."
UPLOAD_RESULT=$(curl -s -X POST -F "files=@$TEST_FILE" "$SERVER_URL/api/upload")
echo "✅ Upload result: $UPLOAD_RESULT"

echo ""
echo "4. Testing file download..."
DOWNLOAD_CONTENT=$(curl -s "$SERVER_URL/files/test_upload.txt")
echo "✅ Downloaded content: $DOWNLOAD_CONTENT"

echo ""
echo "5. Testing directory creation..."
MKDIR_RESULT=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"path": "/test_directory"}' "$SERVER_URL/api/mkdir")
echo "✅ Directory creation: $MKDIR_RESULT"

echo ""
echo "6. Testing system metrics..."
METRICS=$(curl -s "$SERVER_URL/metrics")
UPTIME=$(echo "$METRICS" | jq -r '.uptime')
MEMORY=$(echo "$METRICS" | jq -r '.memory.alloc')
GOROUTINES=$(echo "$METRICS" | jq -r '.goroutines')
echo "✅ System metrics:"
echo "   Uptime: $UPTIME"
echo "   Memory: $MEMORY bytes"
echo "   Goroutines: $GOROUTINES"

echo ""
echo "7. Testing file deletion..."
DELETE_RESULT=$(curl -s -X DELETE "$SERVER_URL/api/files/test_upload.txt")
echo "✅ Delete result: $DELETE_RESULT"

echo ""
echo "8. Final file listing..."
FINAL_FILES=$(curl -s "$SERVER_URL/api/files/")
echo "✅ Final file count: $(echo "$FINAL_FILES" | jq -r '.total')"

echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "📊 Server Performance Summary:"
echo "   - Health check: ✅"
echo "   - File listing: ✅"
echo "   - File upload: ✅"
echo "   - File download: ✅"
echo "   - Directory creation: ✅"
echo "   - System metrics: ✅"
echo "   - File deletion: ✅"

# Clean up
rm -f "$TEST_FILE"

echo ""
echo "🌐 Web Interface: $SERVER_URL"
echo "📚 API Documentation: See README.md"
