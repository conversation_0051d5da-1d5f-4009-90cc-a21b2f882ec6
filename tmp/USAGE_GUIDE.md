# 文件服务器使用指南

## 🚀 快速开始

### 1. 启动服务器
```bash
# 使用默认配置启动
./fileserver

# 使用自定义配置启动
./fileserver -config /path/to/config.yaml

# 查看帮助信息
./fileserver -help

# 查看版本信息
./fileserver -version
```

### 2. 访问Web界面
打开浏览器访问: http://localhost:8080

## 📡 API 接口

### 健康检查
```bash
curl http://localhost:8080/health
```

### 文件管理

#### 列出文件和目录
```bash
# 列出根目录
curl http://localhost:8080/api/files/

# 列出指定目录
curl http://localhost:8080/api/files/subfolder/
```

#### 上传文件
```bash
# 上传单个文件到根目录
curl -X POST -F "files=@/path/to/file.txt" http://localhost:8080/api/upload

# 上传文件到指定目录
curl -X POST -F "files=@/path/to/file.txt" -F "path=/uploads/" http://localhost:8080/api/upload

# 上传多个文件
curl -X POST -F "files=@file1.txt" -F "files=@file2.txt" http://localhost:8080/api/upload
```

#### 下载文件
```bash
# 下载文件
curl -O http://localhost:8080/files/filename.txt

# 断点续传下载
curl -C - -O http://localhost:8080/files/largefile.zip
```

#### 创建目录
```bash
curl -X POST -H "Content-Type: application/json" \
     -d '{"path": "/new_directory"}' \
     http://localhost:8080/api/mkdir
```

#### 删除文件或目录
```bash
curl -X DELETE http://localhost:8080/api/files/filename.txt
curl -X DELETE http://localhost:8080/api/files/directory_name/
```

#### 移动文件
```bash
curl -X POST -H "Content-Type: application/json" \
     -d '{"from": "/old/path.txt", "to": "/new/path.txt"}' \
     http://localhost:8080/api/move
```

### 系统监控
```bash
# 获取系统指标
curl http://localhost:8080/metrics
```

## 🔧 配置说明

### 主要配置项

#### 服务器配置
```yaml
server:
  host: "0.0.0.0"        # 监听地址
  port: 8080             # 监听端口
  read_timeout: 30s      # 读取超时
  write_timeout: 30s     # 写入超时
```

#### 存储配置
```yaml
storage:
  root_path: "./data"           # 文件存储根目录
  max_file_size: 1073741824     # 最大文件大小 (1GB)
  allowed_extensions: []        # 允许的文件扩展名 (空表示全部允许)
  temp_dir: "./tmp"            # 临时文件目录
```

#### 安全配置
```yaml
security:
  enable_auth: false           # 是否启用认证
  enable_cors: true           # 是否启用CORS
  allowed_origins: ["*"]      # 允许的来源
  max_request_size: 104857600 # 最大请求大小 (100MB)
```

## 🌟 功能特性

### ✅ 已实现功能
- ✅ **静态文件服务**: 高效的文件下载和访问
- ✅ **文件上传**: 支持单文件和多文件上传
- ✅ **文件管理**: 创建、删除、移动文件和目录
- ✅ **Web界面**: 直观的文件浏览和管理界面
- ✅ **API接口**: 完整的RESTful API
- ✅ **系统监控**: 实时性能指标
- ✅ **CORS支持**: 跨域资源共享
- ✅ **安全检查**: 路径安全验证
- ✅ **日志记录**: 详细的操作日志
- ✅ **配置管理**: 灵活的YAML配置

### 🚧 计划中功能
- 🚧 **分块上传**: 大文件分片上传和断点续传
- 🚧 **文件压缩**: 文件夹ZIP压缩下载
- 🚧 **全文搜索**: 基于文件名和内容的搜索
- 🚧 **用户认证**: JWT/OAuth2.0认证
- 🚧 **权限控制**: 细粒度权限管理
- 🚧 **WebDAV支持**: 标准WebDAV协议
- 🚧 **HTTPS支持**: SSL/TLS加密传输
- 🚧 **速度限制**: 上传下载速度控制

## 🧪 测试

### 运行测试脚本
```bash
# 确保服务器正在运行
./fileserver &

# 运行测试脚本
./tmp/test_server.sh
```

### 手动测试步骤
1. 启动服务器: `./fileserver`
2. 访问Web界面: http://localhost:8080
3. 测试文件上传和下载
4. 检查API响应: `curl http://localhost:8080/health`

## 📊 性能指标

### 当前性能
- **并发连接**: 支持数千个并发连接
- **文件大小**: 默认支持最大1GB文件
- **内存使用**: 低内存占用，高效的流式处理
- **响应时间**: 毫秒级API响应

### 监控指标
- 系统内存使用情况
- Goroutine数量
- 请求响应时间
- 文件传输速度

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :8080

# 修改配置文件中的端口
vim configs/config.yaml
```

#### 2. 权限问题
```bash
# 确保数据目录有写权限
chmod 755 data/
```

#### 3. 文件上传失败
- 检查文件大小是否超过限制
- 确认文件扩展名是否被允许
- 查看服务器日志获取详细错误信息

## 📝 开发说明

### 项目结构
```
fileserver/
├── cmd/server/          # 应用程序入口
├── internal/           # 内部包
│   ├── server/        # HTTP服务器
│   ├── handlers/      # 请求处理器
│   ├── middleware/    # 中间件
│   └── config/        # 配置管理
├── web/               # 前端资源
├── configs/           # 配置文件
└── tmp/              # 测试脚本和文档
```

### 扩展开发
- 添加新的API端点到 `internal/handlers/`
- 实现新的中间件到 `internal/middleware/`
- 修改配置结构到 `internal/config/`

## 📞 支持

如有问题或建议，请查看项目文档或提交Issue。
