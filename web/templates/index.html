<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .upload-area {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }

        .upload-area.dragover {
            border-color: #2ecc71;
            background-color: #e8f5e9;
        }

        .upload-icon {
            font-size: 48px;
            color: #bdc3c7;
            margin-bottom: 10px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #2ecc71;
        }

        .btn-success:hover {
            background: #27ae60;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-info {
            display: flex;
            align-items: center;
        }

        .file-icon {
            margin-right: 10px;
            font-size: 18px;
        }

        .file-name {
            font-weight: 500;
        }

        .file-size {
            color: #7f8c8d;
            font-size: 12px;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .api-info {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }

        .api-info h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .api-endpoint {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 5px 8px;
            border-radius: 3px;
            margin: 5px 0;
            display: block;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 High-Performance File Server</h1>
            <p>Upload, download, and manage your files with ease</p>
        </div>

        <div class="main-content">
            <div class="card">
                <h2>📤 Upload Files</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <p>Drag and drop files here or click to select</p>
                    <input type="file" id="fileInput" class="file-input" multiple>
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        Select Files
                    </button>
                </div>
                <div id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Uploading...</p>
                </div>
                <div id="uploadStatus"></div>
            </div>

            <div class="card">
                <h2>📂 File Browser</h2>
                <div style="margin-bottom: 15px;">
                    <button class="btn" onclick="loadFiles()">🔄 Refresh</button>
                    <button class="btn btn-success" onclick="createFolder()">📁 New Folder</button>
                </div>
                <div class="file-list" id="fileList">
                    <p>Loading files...</p>
                </div>
            </div>
        </div>

        <div class="api-info">
            <h3>🔗 API Endpoints</h3>
            <p>You can also interact with the server using these REST API endpoints:</p>
            <code class="api-endpoint">GET /api/files/ - List files and directories</code>
            <code class="api-endpoint">POST /api/upload - Upload files</code>
            <code class="api-endpoint">POST /api/mkdir - Create directory</code>
            <code class="api-endpoint">GET /files/path/to/file - Download files</code>
            <code class="api-endpoint">GET /metrics - System metrics</code>
        </div>
    </div>

    <script>
        // File upload functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const uploadStatus = document.getElementById('uploadStatus');

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }

            uploadProgress.style.display = 'block';
            uploadStatus.innerHTML = '';

            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                uploadProgress.style.display = 'none';
                if (data.uploaded_files && data.uploaded_files.length > 0) {
                    showStatus(`Successfully uploaded ${data.uploaded_files.length} file(s)`, 'success');
                    loadFiles(); // Refresh file list
                } else {
                    showStatus('Upload failed', 'error');
                }
                if (data.errors && data.errors.length > 0) {
                    showStatus(`Errors: ${data.errors.join(', ')}`, 'error');
                }
            })
            .catch(error => {
                uploadProgress.style.display = 'none';
                showStatus(`Upload error: ${error.message}`, 'error');
            });
        }

        function showStatus(message, type) {
            uploadStatus.innerHTML = `<div class="status-message status-${type}">${message}</div>`;
        }

        // File browser functionality
        function loadFiles(path = '/') {
            fetch(`/api/files${path}`)
                .then(response => response.json())
                .then(data => {
                    displayFiles(data.files || []);
                })
                .catch(error => {
                    document.getElementById('fileList').innerHTML = `<p>Error loading files: ${error.message}</p>`;
                });
        }

        function displayFiles(files) {
            const fileList = document.getElementById('fileList');
            if (files.length === 0) {
                fileList.innerHTML = '<p>No files found</p>';
                return;
            }

            const html = files.map(file => `
                <div class="file-item">
                    <div class="file-info">
                        <span class="file-icon">${file.is_dir ? '📁' : '📄'}</span>
                        <div>
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${file.is_dir ? 'Directory' : formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <div class="file-actions">
                        ${!file.is_dir ? `<button class="btn btn-small" onclick="downloadFile('${file.path}')">⬇️ Download</button>` : ''}
                        <button class="btn btn-small" onclick="deleteFile('${file.path}')">🗑️ Delete</button>
                    </div>
                </div>
            `).join('');

            fileList.innerHTML = html;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function downloadFile(path) {
            window.open(`/files${path}`, '_blank');
        }

        function deleteFile(path) {
            if (confirm(`Are you sure you want to delete ${path}?`)) {
                fetch(`/api/files${path}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        showStatus(data.message, 'success');
                        loadFiles();
                    } else {
                        showStatus('Delete failed', 'error');
                    }
                })
                .catch(error => {
                    showStatus(`Delete error: ${error.message}`, 'error');
                });
            }
        }

        function createFolder() {
            const name = prompt('Enter folder name:');
            if (name) {
                fetch('/api/mkdir', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ path: `/${name}` })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        showStatus(data.message, 'success');
                        loadFiles();
                    } else {
                        showStatus('Create folder failed', 'error');
                    }
                })
                .catch(error => {
                    showStatus(`Create folder error: ${error.message}`, 'error');
                });
            }
        }

        // Load files on page load
        loadFiles();
    </script>
</body>
</html>
