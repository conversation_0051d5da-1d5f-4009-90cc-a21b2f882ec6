package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Server     ServerConfig     `yaml:"server"`
	Storage    StorageConfig    `yaml:"storage"`
	Upload     UploadConfig     `yaml:"upload"`
	Download   DownloadConfig   `yaml:"download"`
	Security   SecurityConfig   `yaml:"security"`
	Logging    LoggingConfig    `yaml:"logging"`
	Cache      CacheConfig      `yaml:"cache"`
	WebDAV     WebDAVConfig     `yaml:"webdav"`
	Monitoring MonitoringConfig `yaml:"monitoring"`
}

type ServerConfig struct {
	Host           string        `yaml:"host"`
	Port           int           `yaml:"port"`
	ReadTimeout    time.Duration `yaml:"read_timeout"`
	WriteTimeout   time.Duration `yaml:"write_timeout"`
	IdleTimeout    time.Duration `yaml:"idle_timeout"`
	MaxHeaderBytes int           `yaml:"max_header_bytes"`
}

type StorageConfig struct {
	RootPath          string   `yaml:"root_path"`
	MaxFileSize       int64    `yaml:"max_file_size"`
	AllowedExtensions []string `yaml:"allowed_extensions"`
	TempDir           string   `yaml:"temp_dir"`
}

type UploadConfig struct {
	ChunkSize            int64 `yaml:"chunk_size"`
	MaxConcurrentUploads int   `yaml:"max_concurrent_uploads"`
	EnableResume         bool  `yaml:"enable_resume"`
	EnableMD5Check       bool  `yaml:"enable_md5_check"`
}

type DownloadConfig struct {
	EnableRangeRequests      bool `yaml:"enable_range_requests"`
	MaxConcurrentDownloads   int  `yaml:"max_concurrent_downloads"`
	RateLimitMBPerSec        int  `yaml:"rate_limit_mb_per_sec"`
}

type SecurityConfig struct {
	EnableAuth     bool          `yaml:"enable_auth"`
	JWTSecret      string        `yaml:"jwt_secret"`
	SessionTimeout time.Duration `yaml:"session_timeout"`
	EnableCORS     bool          `yaml:"enable_cors"`
	AllowedOrigins []string      `yaml:"allowed_origins"`
	MaxRequestSize int64         `yaml:"max_request_size"`
}

type LoggingConfig struct {
	Level    string `yaml:"level"`
	Format   string `yaml:"format"`
	Output   string `yaml:"output"`
	FilePath string `yaml:"file_path"`
}

type CacheConfig struct {
	Enable        bool `yaml:"enable"`
	MaxAge        int  `yaml:"max_age"`
	StaticMaxAge  int  `yaml:"static_max_age"`
}

type WebDAVConfig struct {
	Enable bool   `yaml:"enable"`
	Prefix string `yaml:"prefix"`
}

type MonitoringConfig struct {
	EnableMetrics bool   `yaml:"enable_metrics"`
	MetricsPath   string `yaml:"metrics_path"`
	EnablePprof   bool   `yaml:"enable_pprof"`
	PprofPrefix   string `yaml:"pprof_prefix"`
}

// LoadConfig loads configuration from file
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Set defaults
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Storage.RootPath == "" {
		config.Storage.RootPath = "./data"
	}
	if config.Storage.TempDir == "" {
		config.Storage.TempDir = "./tmp"
	}

	return &config, nil
}

// GetAddress returns the server address
func (c *Config) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}
