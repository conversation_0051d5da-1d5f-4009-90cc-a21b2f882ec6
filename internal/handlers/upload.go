package handlers

import (
	"crypto/md5"
	"fmt"
	"hash"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"fileserver/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// UploadHandler handles file upload operations
type UploadHandler struct {
	config *config.Config
	logger *logrus.Logger
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(cfg *config.Config, logger *logrus.Logger) *UploadHandler {
	return &UploadHandler{
		config: cfg,
		logger: logger,
	}
}

// UploadFile handles simple file upload
func (h *UploadHandler) UploadFile(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(h.config.Security.MaxRequestSize); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse multipart form"})
		return
	}

	// Get target directory from form or query
	targetDir := c.PostForm("path")
	if targetDir == "" {
		targetDir = c.Query("path")
	}
	if targetDir == "" {
		targetDir = "/"
	}

	cleanTargetDir := filepath.Clean(targetDir)
	if cleanTargetDir == "." {
		cleanTargetDir = "/"
	}

	// Remove leading slash for filepath.Join to work correctly
	relativePath := strings.TrimPrefix(cleanTargetDir, "/")
	fullTargetDir := filepath.Join(h.config.Storage.RootPath, relativePath)

	// Security check
	absRootPath, _ := filepath.Abs(h.config.Storage.RootPath)
	absFullPath, _ := filepath.Abs(fullTargetDir)
	if !strings.HasPrefix(absFullPath, absRootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create target directory if it doesn't exist
	if err := os.MkdirAll(fullTargetDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create target directory"})
		return
	}

	// Get uploaded files
	form := c.Request.MultipartForm
	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	var uploadedFiles []string
	var errors []string

	for _, fileHeader := range files {
		// Check file size
		if fileHeader.Size > h.config.Storage.MaxFileSize {
			errors = append(errors, fmt.Sprintf("File %s exceeds maximum size limit", fileHeader.Filename))
			continue
		}

		// Check file extension if restrictions are set
		if len(h.config.Storage.AllowedExtensions) > 0 {
			ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
			allowed := false
			for _, allowedExt := range h.config.Storage.AllowedExtensions {
				if ext == allowedExt {
					allowed = true
					break
				}
			}
			if !allowed {
				errors = append(errors, fmt.Sprintf("File type %s not allowed for %s", ext, fileHeader.Filename))
				continue
			}
		}

		// Open uploaded file
		src, err := fileHeader.Open()
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to open uploaded file %s", fileHeader.Filename))
			continue
		}

		// Create destination file
		dstPath := filepath.Join(fullTargetDir, fileHeader.Filename)
		dst, err := os.Create(dstPath)
		if err != nil {
			src.Close()
			errors = append(errors, fmt.Sprintf("Failed to create file %s", fileHeader.Filename))
			continue
		}

		// Copy file content
		_, err = io.Copy(dst, src)
		src.Close()
		dst.Close()

		if err != nil {
			os.Remove(dstPath) // Clean up on error
			errors = append(errors, fmt.Sprintf("Failed to save file %s", fileHeader.Filename))
			continue
		}

		uploadedFiles = append(uploadedFiles, fileHeader.Filename)
		h.logger.Infof("File uploaded successfully: %s", dstPath)
	}

	response := gin.H{
		"uploaded_files": uploadedFiles,
		"total_uploaded": len(uploadedFiles),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["total_errors"] = len(errors)
	}

	if len(uploadedFiles) > 0 {
		c.JSON(http.StatusOK, response)
	} else {
		c.JSON(http.StatusBadRequest, response)
	}
}

// UploadChunk handles chunked file upload
func (h *UploadHandler) UploadChunk(c *gin.Context) {
	// Get chunk parameters
	filename := c.PostForm("filename")
	chunkIndex := c.PostForm("chunkIndex")
	totalChunks := c.PostForm("totalChunks")
	chunkHash := c.PostForm("chunkHash")

	if filename == "" || chunkIndex == "" || totalChunks == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters"})
		return
	}

	chunkIdx, err := strconv.Atoi(chunkIndex)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chunk index"})
		return
	}

	totalChks, err := strconv.Atoi(totalChunks)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid total chunks"})
		return
	}

	// Get target directory
	targetDir := c.PostForm("path")
	if targetDir == "" {
		targetDir = "/"
	}

	cleanTargetDir := filepath.Clean(targetDir)
	fullTargetDir := filepath.Join(h.config.Storage.RootPath, cleanTargetDir)

	// Security check
	if !strings.HasPrefix(fullTargetDir, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create temp directory for chunks
	tempDir := filepath.Join(h.config.Storage.TempDir, "chunks", filename)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create temp directory"})
		return
	}

	// Get uploaded chunk
	file, err := c.FormFile("chunk")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get chunk file"})
		return
	}

	// Open chunk file
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to open chunk file"})
		return
	}
	defer src.Close()

	// Verify chunk hash if provided
	if chunkHash != "" && h.config.Upload.EnableMD5Check {
		hash := md5.New()
		if _, err := io.Copy(hash, src); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to calculate chunk hash"})
			return
		}
		
		calculatedHash := fmt.Sprintf("%x", hash.Sum(nil))
		if calculatedHash != chunkHash {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Chunk hash mismatch"})
			return
		}

		// Reset reader
		src.Seek(0, 0)
	}

	// Save chunk to temp file
	chunkPath := filepath.Join(tempDir, fmt.Sprintf("chunk_%d", chunkIdx))
	dst, err := os.Create(chunkPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create chunk file"})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save chunk"})
		return
	}

	h.logger.Infof("Chunk %d/%d saved for file %s", chunkIdx+1, totalChks, filename)

	c.JSON(http.StatusOK, gin.H{
		"message":      "Chunk uploaded successfully",
		"chunk_index":  chunkIdx,
		"total_chunks": totalChks,
		"filename":     filename,
	})
}

// CompleteUpload assembles chunks into final file
func (h *UploadHandler) CompleteUpload(c *gin.Context) {
	var req struct {
		Filename    string `json:"filename" binding:"required"`
		TotalChunks int    `json:"total_chunks" binding:"required"`
		Path        string `json:"path"`
		FileHash    string `json:"file_hash"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get target directory
	targetDir := req.Path
	if targetDir == "" {
		targetDir = "/"
	}

	cleanTargetDir := filepath.Clean(targetDir)
	fullTargetDir := filepath.Join(h.config.Storage.RootPath, cleanTargetDir)

	// Security check
	if !strings.HasPrefix(fullTargetDir, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create target directory
	if err := os.MkdirAll(fullTargetDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create target directory"})
		return
	}

	// Check if all chunks exist
	tempDir := filepath.Join(h.config.Storage.TempDir, "chunks", req.Filename)
	for i := 0; i < req.TotalChunks; i++ {
		chunkPath := filepath.Join(tempDir, fmt.Sprintf("chunk_%d", i))
		if _, err := os.Stat(chunkPath); os.IsNotExist(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Missing chunk %d", i),
			})
			return
		}
	}

	// Create final file
	finalPath := filepath.Join(fullTargetDir, req.Filename)
	finalFile, err := os.Create(finalPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create final file"})
		return
	}
	defer finalFile.Close()

	// Assemble chunks
	var hash hash.Hash
	if req.FileHash != "" && h.config.Upload.EnableMD5Check {
		hash = md5.New()
	}

	for i := 0; i < req.TotalChunks; i++ {
		chunkPath := filepath.Join(tempDir, fmt.Sprintf("chunk_%d", i))
		chunkFile, err := os.Open(chunkPath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to open chunk %d", i),
			})
			return
		}

		var writer io.Writer = finalFile
		if hash != nil {
			writer = io.MultiWriter(finalFile, hash)
		}

		if _, err := io.Copy(writer, chunkFile); err != nil {
			chunkFile.Close()
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to copy chunk %d", i),
			})
			return
		}
		chunkFile.Close()
	}

	// Verify file hash if provided
	if hash != nil {
		calculatedHash := fmt.Sprintf("%x", hash.Sum(nil))
		if calculatedHash != req.FileHash {
			os.Remove(finalPath) // Clean up on hash mismatch
			c.JSON(http.StatusBadRequest, gin.H{"error": "File hash mismatch"})
			return
		}
	}

	// Clean up temp chunks
	os.RemoveAll(tempDir)

	h.logger.Infof("File assembled successfully: %s", finalPath)

	c.JSON(http.StatusOK, gin.H{
		"message":  "File uploaded successfully",
		"filename": req.Filename,
		"path":     filepath.Join(cleanTargetDir, req.Filename),
	})
}
