package handlers

import (
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"fileserver/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// FileHandler handles file operations
type FileHandler struct {
	config *config.Config
	logger *logrus.Logger
}

// NewFileHandler creates a new file handler
func NewFileHandler(cfg *config.Config, logger *logrus.Logger) *FileHandler {
	return &FileHandler{
		config: cfg,
		logger: logger,
	}
}

// FileInfo represents file information
type FileInfo struct {
	Name         string    `json:"name"`
	Path         string    `json:"path"`
	Size         int64     `json:"size"`
	ModTime      time.Time `json:"mod_time"`
	IsDir        bool      `json:"is_dir"`
	MimeType     string    `json:"mime_type,omitempty"`
	Permissions  string    `json:"permissions"`
}

// ListFiles lists files in a directory
func (h *FileHandler) ListFiles(c *gin.Context) {
	requestPath := c.Param("path")
	if requestPath == "" {
		requestPath = "/"
	}

	// Clean and validate path
	cleanPath := filepath.Clean(requestPath)
	if cleanPath == "." {
		cleanPath = "/"
	}

	// Remove leading slash for filepath.Join to work correctly
	relativePath := strings.TrimPrefix(cleanPath, "/")
	fullPath := filepath.Join(h.config.Storage.RootPath, relativePath)

	// Security check: ensure path is within root directory
	absRootPath, _ := filepath.Abs(h.config.Storage.RootPath)
	absFullPath, _ := filepath.Abs(fullPath)
	if !strings.HasPrefix(absFullPath, absRootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Check if path exists
	info, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Path not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to access path"})
		}
		return
	}

	// If it's a file, return file info
	if !info.IsDir() {
		fileInfo := h.getFileInfo(fullPath, info)
		c.JSON(http.StatusOK, fileInfo)
		return
	}

	// If it's a directory, list contents
	entries, err := os.ReadDir(fullPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read directory"})
		return
	}

	var files []FileInfo
	for _, entry := range entries {
		entryPath := filepath.Join(fullPath, entry.Name())
		entryInfo, err := entry.Info()
		if err != nil {
			h.logger.Warnf("Failed to get info for %s: %v", entryPath, err)
			continue
		}

		fileInfo := h.getFileInfo(entryPath, entryInfo)
		files = append(files, fileInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"path":  cleanPath,
		"files": files,
		"total": len(files),
	})
}

// CreateFile creates a new file
func (h *FileHandler) CreateFile(c *gin.Context) {
	requestPath := c.Param("path")
	if requestPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Path is required"})
		return
	}

	cleanPath := filepath.Clean(requestPath)
	fullPath := filepath.Join(h.config.Storage.RootPath, cleanPath)

	// Security check
	if !strings.HasPrefix(fullPath, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create directory"})
		return
	}

	// Create file
	file, err := os.Create(fullPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create file"})
		return
	}
	defer file.Close()

	// Copy content from request body
	if _, err := io.Copy(file, c.Request.Body); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write file content"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "File created successfully", "path": cleanPath})
}

// UpdateFile updates an existing file
func (h *FileHandler) UpdateFile(c *gin.Context) {
	requestPath := c.Param("path")
	if requestPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Path is required"})
		return
	}

	cleanPath := filepath.Clean(requestPath)
	fullPath := filepath.Join(h.config.Storage.RootPath, cleanPath)

	// Security check
	if !strings.HasPrefix(fullPath, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Check if file exists
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Open file for writing
	file, err := os.OpenFile(fullPath, os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to open file"})
		return
	}
	defer file.Close()

	// Copy content from request body
	if _, err := io.Copy(file, c.Request.Body); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write file content"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File updated successfully", "path": cleanPath})
}

// DeleteFile deletes a file or directory
func (h *FileHandler) DeleteFile(c *gin.Context) {
	requestPath := c.Param("path")
	if requestPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Path is required"})
		return
	}

	cleanPath := filepath.Clean(requestPath)
	if cleanPath == "." {
		cleanPath = "/"
	}

	// Remove leading slash for filepath.Join to work correctly
	relativePath := strings.TrimPrefix(cleanPath, "/")
	fullPath := filepath.Join(h.config.Storage.RootPath, relativePath)

	// Security check
	absRootPath, _ := filepath.Abs(h.config.Storage.RootPath)
	absFullPath, _ := filepath.Abs(fullPath)
	if !strings.HasPrefix(absFullPath, absRootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Check if path exists
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "Path not found"})
		return
	}

	// Delete file or directory
	if err := os.RemoveAll(fullPath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete path"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Path deleted successfully", "path": cleanPath})
}

// CreateDirectory creates a new directory
func (h *FileHandler) CreateDirectory(c *gin.Context) {
	var req struct {
		Path string `json:"path" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	cleanPath := filepath.Clean(req.Path)
	fullPath := filepath.Join(h.config.Storage.RootPath, cleanPath)

	// Security check
	if !strings.HasPrefix(fullPath, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create directory
	if err := os.MkdirAll(fullPath, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create directory"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Directory created successfully", "path": cleanPath})
}

// MoveFile moves a file or directory
func (h *FileHandler) MoveFile(c *gin.Context) {
	var req struct {
		From string `json:"from" binding:"required"`
		To   string `json:"to" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	fromPath := filepath.Join(h.config.Storage.RootPath, filepath.Clean(req.From))
	toPath := filepath.Join(h.config.Storage.RootPath, filepath.Clean(req.To))

	// Security checks
	if !strings.HasPrefix(fromPath, h.config.Storage.RootPath) || !strings.HasPrefix(toPath, h.config.Storage.RootPath) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Move file
	if err := os.Rename(fromPath, toPath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to move file"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File moved successfully"})
}

// CopyFile copies a file or directory
func (h *FileHandler) CopyFile(c *gin.Context) {
	// TODO: Implement file copying
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Copy operation not implemented yet"})
}

// SearchFiles searches for files
func (h *FileHandler) SearchFiles(c *gin.Context) {
	// TODO: Implement file search
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Search operation not implemented yet"})
}

// getFileInfo extracts file information
func (h *FileHandler) getFileInfo(path string, info os.FileInfo) FileInfo {
	relativePath, _ := filepath.Rel(h.config.Storage.RootPath, path)
	if relativePath == "." {
		relativePath = "/"
	} else {
		relativePath = "/" + filepath.ToSlash(relativePath)
	}

	fileInfo := FileInfo{
		Name:        info.Name(),
		Path:        relativePath,
		Size:        info.Size(),
		ModTime:     info.ModTime(),
		IsDir:       info.IsDir(),
		Permissions: info.Mode().String(),
	}

	// Add MIME type for files
	if !info.IsDir() {
		fileInfo.MimeType = getMimeType(path)
	}

	return fileInfo
}

// getMimeType returns the MIME type of a file
func getMimeType(path string) string {
	ext := strings.ToLower(filepath.Ext(path))
	switch ext {
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".pdf":
		return "application/pdf"
	case ".zip":
		return "application/zip"
	case ".tar":
		return "application/x-tar"
	case ".gz":
		return "application/gzip"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".svg":
		return "image/svg+xml"
	case ".mp4":
		return "video/mp4"
	case ".mp3":
		return "audio/mpeg"
	case ".txt":
		return "text/plain"
	case ".md":
		return "text/markdown"
	default:
		return "application/octet-stream"
	}
}
