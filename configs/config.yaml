# File Server Configuration

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s
  max_header_bytes: 1048576  # 1MB

# File storage settings
storage:
  root_path: "./data"
  max_file_size: 1073741824  # 1GB
  allowed_extensions: []  # Empty means all extensions allowed
  temp_dir: "./tmp"

# Upload settings
upload:
  chunk_size: 1048576  # 1MB chunks
  max_concurrent_uploads: 10
  enable_resume: true
  enable_md5_check: true

# Download settings
download:
  enable_range_requests: true
  max_concurrent_downloads: 50
  rate_limit_mb_per_sec: 0  # 0 means no limit

# Security settings
security:
  enable_auth: false
  jwt_secret: "your-secret-key-change-this"
  session_timeout: 24h
  enable_cors: true
  allowed_origins: ["*"]
  max_request_size: 104857600  # 100MB

# Logging
logging:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  file_path: "./logs/server.log"

# Cache settings
cache:
  enable: true
  max_age: 3600  # 1 hour
  static_max_age: 86400  # 24 hours

# WebDAV settings
webdav:
  enable: false
  prefix: "/webdav"

# Monitoring
monitoring:
  enable_metrics: true
  metrics_path: "/metrics"
  enable_pprof: false
  pprof_prefix: "/debug/pprof"
