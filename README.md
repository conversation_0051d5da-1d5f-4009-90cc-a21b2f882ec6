# 高性能文件服务器

一个基于Go实现的高性能文件服务器，具备完整的文件管理、上传下载、安全认证等功能。

## 功能特性

### 核心功能
- 🚀 **静态文件服务**: 高效提供静态资源访问，支持MIME类型、缓存控制和范围请求
- 📦 **文件夹压缩下载**: 实时生成ZIP压缩包，支持选择性打包和进度显示
- ⬆️ **智能文件上传**: 支持拖拽上传、分块上传、断点续传和MD5校验
- 📝 **文件管理**: 内置代码编辑器，支持语法高亮和实时保存
- 🔍 **全文搜索**: 基于文件名和内容的模糊搜索，支持正则表达式
- ⚡ **传输控制**: 分片下载、多线程并发和速度限制
- 🔒 **安全体系**: JWT/OAuth2.0认证、细粒度权限控制、操作审计
- 🌐 **HTTPS支持**: 自动配置Let's Encrypt证书，支持TLS 1.3
- 📁 **WebDAV集成**: 完全兼容RFC 4918标准
- 🖥️ **命令行友好**: 提供标准RESTful接口

### 系统特性
- 跨平台兼容 (Linux/Windows/macOS)
- 高性能异步I/O，支持10K+并发连接
- 插件架构支持自定义中间件
- 实时监控和性能指标

## 快速开始

### 安装依赖
```bash
go mod tidy
```

### 运行服务器
```bash
go run cmd/server/main.go
```

### 配置文件
服务器配置文件位于 `configs/config.yaml`

## API 示例

### 文件上传
```bash
curl -T file.txt http://localhost:8080/api/upload/
```

### 文件下载
```bash
curl -C - -O http://localhost:8080/files/path/file.zip
```

### 创建目录
```bash
curl -X POST http://localhost:8080/api/mkdir -d '{"path": "/newfolder"}'
```

## 开发

### 项目结构
```
fileserver/
├── cmd/                    # 应用程序入口
├── internal/              # 内部包
│   ├── server/           # HTTP服务器
│   ├── handlers/         # 请求处理器
│   ├── middleware/       # 中间件
│   ├── auth/            # 认证模块
│   └── storage/         # 存储层
├── pkg/                  # 公共包
├── web/                  # 前端资源
├── configs/              # 配置文件
├── docs/                 # 文档
└── tmp/                  # 临时文件和测试脚本
```

## 许可证

MIT License
